package llm

import (
	"brainHub/internal/consts"
	"brainHub/internal/model"
	"fmt"
)

type LLMsConfig struct {
	Common CommonConfig     `json:"common"`
	Vertex VertexConfig     `json:"vertex"`
	AoAi   *model.LLMParams `json:"aoai"`
}
type CommonConfig struct {
	MaxOutputTokens int32   `json:"max_output_tokens"`
	Temperature     float32 `json:"temperature"`
}
type VertexConfig struct {
	Region         string       `json:"region"`
	ProjectID      string       `json:"project_id"`
	CredentialFile string       `json:"credential_file"`
	LLMType        string       `json:"llm_type"`
	Gemini         GeminiConfig `json:"gemini"`
	ThirdModel     ModelConfig  `json:"third_model"`
}
type ModelConfig struct {
	Model           string  `json:"model"`
	APIVersion      string  `json:"api_version"`
	Temperature     float32 `json:"temperature"`
	MaxOutputTokens int32   `json:"max_output_tokens"`
}

type GeminiConfig struct {
	Model           string  `json:"model"`
	Temperature     float32 `json:"temperature"`
	MaxOutputTokens int32   `json:"max_output_tokens"`
	IncludeThoughts bool    `json:"include_thoughts"`
	ThinkingBudget  int32   `json:"thinking_budget"`
}

type ResponseData struct {
	Response        any    `json:"response"`
	TotalTokenCount int32  `json:"total_token_count"`
	TenantID        string `json:"tenant_id"`
	UserID          string `json:"user_id"`
	ServiceID       string `json:"service_id"`
	Channel         string `json:"channel"`
}

func (c *LLMsConfig) VertexEndPointForClaude() string {
	// https://REGION-aiplatform.googleapis.com/v1/projects/PROJECT_ID/locations/REGION
	// https://REGION-aiplatform.googleapis.com/v1/projects/PROJECT_ID/locations/REGION/publishers/anthropic/models/MODEL_NAME:rawPredict
	if c.Vertex.LLMType == consts.VertexAIClaude {
		return fmt.Sprintf(`https://%s-aiplatform.googleapis.com/v1/projects/%s/locations/%s/publishers/anthropic/models/%s:rawPredict`,
			c.Vertex.Region,
			c.Vertex.ProjectID,
			c.Vertex.Region,
			c.Vertex.ThirdModel.Model,
		)

	}

	return ""
}

type Payload struct {
	Attachments       *model.Asset
	SystemInstruction string
	History           any
}
