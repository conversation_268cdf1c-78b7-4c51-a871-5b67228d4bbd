package claude

import "github.com/gogf/gf/v2/encoding/gjson"

// ClaudeMessage Claude 消息結構
// 用於表示 Claude API 中的單個消息
type ClaudeMessage struct {
	Role    string        `json:"role"`    // 角色：user 或 assistant
	Content []ContentPart `json:"content"` // 消息內容
}

// MarshalJSON 自定義 JSON 序列化，確保 ContentPart 正確序列化
func (cm ClaudeMessage) MarshalJSON() ([]byte, error) {
	// 手動序列化 Content 數組
	contentJsonArray := make([]map[string]interface{}, len(cm.Content))
	for i, part := range cm.Content {
		switch part.Type {
		case "text":
			contentJsonArray[i] = map[string]interface{}{
				"type": part.Type,
				"text": part.Text,
			}
		case "image":
			contentJsonArray[i] = map[string]interface{}{
				"type":   part.Type,
				"source": part.Source,
			}
		default:
			// 預設情況
			contentData := map[string]interface{}{
				"type": part.Type,
			}
			if part.Text != "" {
				contentData["text"] = part.Text
			}
			if part.Source != nil {
				contentData["source"] = part.Source
			}
			contentJsonArray[i] = contentData
		}
	}

	// 構建完整的消息對象
	messageData := map[string]interface{}{
		"role":    cm.Role,
		"content": contentJsonArray,
	}

	jsonStr := gjson.New(messageData).MustToJsonString()
	return []byte(jsonStr), nil
}

// ContentPart 內容部分
// 支援文本和圖片等多種內容類型，根據 Google Vertex AI Claude API 規範設計
type ContentPart struct {
	Type   string       `json:"type"`             // 內容類型：text, image
	Text   string       `json:"text,omitempty"`   // 文本內容（僅當 type="text" 時使用）
	Source *ImageSource `json:"source,omitempty"` // 圖片來源（僅當 type="image" 時使用）
}

// MarshalJSON 自定義 JSON 序列化，確保根據類型只包含相關字段
func (cp ContentPart) MarshalJSON() ([]byte, error) {
	switch cp.Type {
	case "text":
		// 文本類型只包含 type 和 text 字段
		jsonStr := gjson.New(map[string]interface{}{
			"type": cp.Type,
			"text": cp.Text,
		}).MustToJsonString()
		return []byte(jsonStr), nil
	case "image":
		// 圖片類型只包含 type 和 source 字段
		jsonStr := gjson.New(map[string]interface{}{
			"type":   cp.Type,
			"source": cp.Source,
		}).MustToJsonString()
		return []byte(jsonStr), nil
	default:
		// 預設情況，包含所有非空字段
		result := map[string]interface{}{
			"type": cp.Type,
		}
		if cp.Text != "" {
			result["text"] = cp.Text
		}
		if cp.Source != nil {
			result["source"] = cp.Source
		}
		jsonStr := gjson.New(result).MustToJsonString()
		return []byte(jsonStr), nil
	}
}

// ImageSource 圖片來源
// 用於 base64 編碼的圖片數據
type ImageSource struct {
	Type      string `json:"type"`       // 固定為 "base64"
	MediaType string `json:"media_type"` // MIME 類型
	Data      string `json:"data"`       // Base64 編碼的圖片數據
}

// ClaudeRequest Claude API 請求結構
// 包含完整的 API 請求參數，符合 Google Vertex AI Claude API 規範
type ClaudeRequest struct {
	AnthropicVersion string          `json:"anthropic_version"` // API 版本
	MaxTokens        int32           `json:"max_tokens"`        // 最大 token 數
	Temperature      float32         `json:"temperature"`       // 溫度參數
	System           string          `json:"system,omitempty"`  // 系統指令
	Messages         []ClaudeMessage `json:"messages"`          // 消息列表
	Stream           bool            `json:"stream"`            // 是否流式響應
}

// MarshalJSON 自定義 JSON 序列化，確保所有嵌套結構正確序列化
func (cr ClaudeRequest) MarshalJSON() ([]byte, error) {
	// 手動序列化 Messages 數組
	messagesJsonArray := make([]map[string]interface{}, len(cr.Messages))
	for i, message := range cr.Messages {
		// 手動序列化每個消息的內容
		contentArray := make([]map[string]interface{}, len(message.Content))
		for j, part := range message.Content {
			switch part.Type {
			case "text":
				contentArray[j] = map[string]interface{}{
					"type": part.Type,
					"text": part.Text,
				}
			case "image":
				contentArray[j] = map[string]interface{}{
					"type":   part.Type,
					"source": part.Source,
				}
			default:
				// 預設情況
				contentData := map[string]interface{}{
					"type": part.Type,
				}
				if part.Text != "" {
					contentData["text"] = part.Text
				}
				if part.Source != nil {
					contentData["source"] = part.Source
				}
				contentArray[j] = contentData
			}
		}

		messagesJsonArray[i] = map[string]interface{}{
			"role":    message.Role,
			"content": contentArray,
		}
	}

	// 構建完整的請求對象
	requestData := map[string]interface{}{
		"anthropic_version": cr.AnthropicVersion,
		"max_tokens":        cr.MaxTokens,
		"temperature":       cr.Temperature,
		"messages":          messagesJsonArray,
		"stream":            cr.Stream,
	}

	// 只有當 System 不為空時才添加
	if cr.System != "" {
		requestData["system"] = cr.System
	}

	jsonStr := gjson.New(requestData).MustToJsonString()
	return []byte(jsonStr), nil
}

// ClaudeResponse Claude API 響應結構
// 包含 API 返回的完整響應信息
type ClaudeResponse struct {
	ID           string                  `json:"id"`
	Type         string                  `json:"type"`
	Role         string                  `json:"role"`
	Content      []ClaudeResponseContent `json:"content"`
	Model        string                  `json:"model"`
	StopReason   string                  `json:"stop_reason"`
	StopSequence string                  `json:"stop_sequence"`
	Usage        ClaudeUsage             `json:"usage"`
}

// ClaudeResponseContent 響應內容
// 表示 API 響應中的內容部分
type ClaudeResponseContent struct {
	Type string `json:"type"`
	Text string `json:"text"`
}

// ClaudeUsage token 使用情況
// 記錄 API 調用的 token 消耗統計
type ClaudeUsage struct {
	InputTokens  int32 `json:"input_tokens"`
	OutputTokens int32 `json:"output_tokens"`
}
