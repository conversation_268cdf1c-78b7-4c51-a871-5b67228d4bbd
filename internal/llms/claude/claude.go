package claude

import (
	"brainHub/internal/consts"
	"brainHub/internal/llms"
	"brainHub/internal/model"
	"brainHub/internal/model/claude"
	"brainHub/internal/model/llm"
	"brainHub/utility"
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/gabriel-vasile/mimetype"
	"github.com/gogf/gf/v2/container/garray"
	"github.com/gogf/gf/v2/encoding/gbase64"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"
	"github.com/gogf/gf/v2/os/gcache"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/jwt"
)

// Claude Anthropic Claude AI 服務實現
// 透過 Google Vertex AI 提供安全、高效能的 AI 對話功能
type Claude struct {
	httpClient      *gclient.Client // GoFrame HTTP 客戶端
	endpoint        string          // API 端點
	accessToken     string          // 訪問令牌
	projectID       string          // GCP 項目 ID
	region          string          // GCP 區域
	modelName       string          // 模型名稱
	apiVersion      string          // Anthropic API 版本
	temperature     float32         // 生成溫度參數
	maxOutputTokens int32           // 最大輸出 token 數量
	history         *garray.Array   // 對話歷史記錄（線程安全），使用 garray.Array 避免死鎖問題
	payload         *llm.Payload    // 初始化載荷
	llmsConfig      *llm.LLMsConfig // LLM 配置，用於 token 刷新
}

// logger 返回專用的日誌記錄器
func (c *Claude) logger() glog.ILogger {
	return g.Log().Cat(consts.CatalogClaude)
}

// New 創建新的 Claude 服務實例
// 返回實現 ILLMs 接口的實例
func New() llms.ILLMs {
	client := gclient.New()
	client.SetTimeout(60 * time.Second)

	return &Claude{
		httpClient: client,
		history:    garray.New(true), // 初始化線程安全的 garray
	}
}

// Initialize 初始化 Claude 服務
// 包含完整的參數驗證和錯誤處理
func (c *Claude) Initialize(ctx context.Context, params *llm.LLMsConfig, payload *llm.Payload) (err error) {
	// 安全地記錄配置資訊
	c.logConfigSafely(ctx, params)

	// 參數驗證
	if params == nil {
		err = gerror.New("the params is nil")
		c.logger().Error(ctx, err)
		return
	}

	// 驗證 Vertex 配置
	if g.IsEmpty(params.Vertex.ProjectID) {
		err = gerror.New("vertex project id is required")
		c.logger().Error(ctx, err)
		return
	}
	if g.IsEmpty(params.Vertex.Region) {
		err = gerror.New("vertex region is required")
		c.logger().Error(ctx, err)
		return
	}
	if g.IsEmpty(params.Vertex.ThirdModel.Model) {
		err = gerror.New("claude model name is required")
		c.logger().Error(ctx, err)
		return
	}

	// 設置基本配置
	c.projectID = params.Vertex.ProjectID
	c.region = params.Vertex.Region
	c.modelName = params.Vertex.ThirdModel.Model
	c.temperature = params.Vertex.ThirdModel.Temperature
	c.maxOutputTokens = params.Vertex.ThirdModel.MaxOutputTokens
	c.payload = payload
	c.llmsConfig = params // 儲存配置以供 token 刷新使用

	// 設置 API 版本，Google Vertex AI Claude API 需要特定版本
	if !g.IsEmpty(params.Vertex.ThirdModel.APIVersion) {
		c.apiVersion = params.Vertex.ThirdModel.APIVersion
	} else {
		c.apiVersion = "vertex-2023-10-16" // Google Vertex AI Claude API 專用版本
		c.logger().Debugf(ctx, "Using Google Vertex AI Claude API version: %s", c.apiVersion)
	}

	// 構建 API 端點
	c.endpoint = params.VertexEndPointForClaude()
	if g.IsEmpty(c.endpoint) {
		err = gerror.New("failed to generate claude endpoint")
		c.logger().Error(ctx, err)
		return
	}

	// 獲取訪問令牌
	err = c.initializeAccessToken(ctx, params.Vertex.CredentialFile)
	if err != nil {
		c.logger().Error(ctx, "Failed to initialize access token:", err)
		return
	}

	// 初始化對話歷史記錄（線程安全）
	c.history.Clear() // 清空現有記錄

	// 創建新的對話會話
	err = c.createNewChat(ctx, payload, "")
	if err != nil {
		c.logger().Error(ctx, "Failed to create new chat:", err)
		return
	}

	c.logger().Info(ctx, "Claude service initialized successfully")
	return nil
}

// logConfigSafely 安全地記錄配置資訊（隱藏敏感信息）
func (c *Claude) logConfigSafely(ctx context.Context, params *llm.LLMsConfig) {
	if params == nil {
		return
	}

	safeConfig := map[string]interface{}{
		"project_id":        params.Vertex.ProjectID,
		"region":            params.Vertex.Region,
		"model":             params.Vertex.ThirdModel.Model,
		"temperature":       params.Vertex.ThirdModel.Temperature,
		"max_output_tokens": params.Vertex.ThirdModel.MaxOutputTokens,
		"credential_file":   "***masked***",
	}

	c.logger().Debugf(ctx, "Initializing Claude with config: %v",
		gjson.New(safeConfig).MustToJsonIndentString())
}

// initializeAccessToken 初始化訪問令牌
// 實現 Google Cloud OAuth2 認證，包含 token 緩存機制
func (c *Claude) initializeAccessToken(ctx context.Context, credentialFile string) error {
	c.logger().Debugf(ctx, "Initializing access token from credential file: %s", "***masked***")

	// 驗證憑證文件存在性和可讀性
	if !gfile.IsFile(credentialFile) {
		err := gerror.New("credential file does not exist")
		c.logger().Error(ctx, err)
		return err
	}

	if !gfile.IsReadable(credentialFile) {
		err := gerror.New("credential file is not readable")
		c.logger().Error(ctx, err)
		return err
	}

	// 構建緩存 key
	cacheKey := fmt.Sprintf("claude_token_%s_%s", c.projectID, c.region)
	c.logger().Debugf(ctx, "Using cache key: %s", cacheKey)

	// 檢查緩存中是否存在有效的 token
	if cachedToken, err := gcache.Get(ctx, cacheKey); err == nil && cachedToken != nil {
		tokenStr := gconv.String(cachedToken)
		if !g.IsEmpty(tokenStr) {
			c.accessToken = tokenStr
			c.logger().Debug(ctx, "Using cached access token")
			return nil
		}
	}

	// 從緩存中讀取憑證文件內容
	credentialData := gfile.GetContentsWithCache(credentialFile, time.Minute)
	if g.IsEmpty(credentialData) {
		err := gerror.New("failed to read credential file or file is empty")
		c.logger().Error(ctx, err)
		return err
	}

	c.logger().Debug(ctx, "Credential file loaded successfully")

	// 解析 JSON 憑證數據
	credentialJson := gjson.New(credentialData)
	if credentialJson == nil {
		err := gerror.New("failed to parse credential JSON")
		c.logger().Error(ctx, err)
		return err
	}

	// 提取必要的憑證信息
	clientEmail := credentialJson.Get("client_email").String()
	privateKey := credentialJson.Get("private_key").String()
	tokenURI := credentialJson.Get("token_uri").String()

	if g.IsEmpty(clientEmail) || g.IsEmpty(privateKey) || g.IsEmpty(tokenURI) {
		err := gerror.New("invalid credential file: missing required fields")
		c.logger().Error(ctx, err)
		return err
	}

	c.logger().Debugf(ctx, "Extracted credential info for client: %s", "***masked***")

	// 創建帶 proxy 的 HTTP 客戶端用於 OAuth2 token 獲取
	vProxy, _ := g.Cfg().Get(ctx, "system.proxy")
	var proxyURL *url.URL
	if vProxy != nil && !vProxy.IsEmpty() {
		proxyURL, _ = url.Parse(vProxy.String())
	}

	httpClient := &http.Client{
		Timeout: 30 * time.Second,
	}
	if proxyURL != nil {
		httpClient.Transport = &http.Transport{
			Proxy: http.ProxyURL(proxyURL),
		}

	}
	// 創建 JWT 配置
	jwtConfig := &jwt.Config{
		Email:      clientEmail,
		PrivateKey: []byte(privateKey),
		TokenURL:   tokenURI,
		Scopes:     []string{"https://www.googleapis.com/auth/cloud-platform"},
	}

	// 使用帶 proxy 的 HTTP 客戶端獲取 OAuth2 token
	tokenSource := jwtConfig.TokenSource(context.WithValue(ctx, oauth2.HTTPClient, httpClient))
	token, err := tokenSource.Token()
	if err != nil {
		wrappedErr := gerror.Wrap(err, "failed to obtain OAuth2 token")
		c.logger().Error(ctx, wrappedErr)
		return wrappedErr
	}

	if token == nil || g.IsEmpty(token.AccessToken) {
		err := gerror.New("obtained token is invalid or empty")
		c.logger().Error(ctx, err)
		return err
	}

	// 設置訪問令牌
	c.accessToken = token.AccessToken

	// 緩存 token，設置過期時間為 50 分鐘（Google Cloud token 通常 1 小時過期）
	cacheExpiry := 50 * time.Minute
	err = gcache.Set(ctx, cacheKey, token.AccessToken, cacheExpiry)
	if err != nil {
		// 緩存失敗不影響主要功能，只記錄警告
		c.logger().Warningf(ctx, "Failed to cache access token: %v", err)
	} else {
		c.logger().Debugf(ctx, "Access token cached successfully, expires in: %v", cacheExpiry)
	}

	c.logger().Info(ctx, "Access token initialized successfully")
	return nil
}

// refreshAccessToken 強制刷新訪問令牌
// 清除緩存並重新獲取 OAuth2 token
func (c *Claude) refreshAccessToken(ctx context.Context, credentialFile string) error {
	c.logger().Info(ctx, "Refreshing access token due to authentication failure")

	// 清除緩存中的 token
	cacheKey := fmt.Sprintf("claude_token_%s_%s", c.projectID, c.region)
	_, err := gcache.Remove(ctx, cacheKey)
	if err != nil {
		c.logger().Warningf(ctx, "Failed to clear token cache: %v", err)
	} else {
		c.logger().Debug(ctx, "Token cache cleared successfully")
	}

	// 重新獲取 token
	err = c.initializeAccessToken(ctx, credentialFile)
	if err != nil {
		c.logger().Error(ctx, "Failed to refresh access token:", err)
		return err
	}

	c.logger().Info(ctx, "Access token refreshed successfully")
	return nil
}

// createNewChat 創建新的對話會話
// 支援系統指令和對話摘要的初始化
func (c *Claude) createNewChat(ctx context.Context, payload *llm.Payload, dialogSummary string) (err error) {
	c.logger().Debugf(ctx, "Create new chat with payload: %v, summary: %v",
		gjson.New(payload).MustToJsonIndentString(), dialogSummary)

	// 清空現有歷史記錄（線程安全）
	c.history.Clear()

	// 如果有對話摘要，添加到歷史記錄中
	if !g.IsEmpty(dialogSummary) {
		summaryMessage := claude.ClaudeMessage{
			Role: "user",
			Content: []claude.ContentPart{
				{
					Type: "text",
					Text: fmt.Sprintf("Previous conversation summary: %s", dialogSummary),
				},
			},
		}
		c.history.Append(summaryMessage)
		c.logger().Debug(ctx, "Added conversation summary to chat history")
	}

	// 處理附件內容
	if payload != nil && payload.Attachments != nil {
		err = c.processAttachments(ctx, payload.Attachments)
		if err != nil {
			c.logger().Error(ctx, "Failed to process attachments:", err)
			return err
		}
	}

	c.logger().Debug(ctx, "New chat session created successfully")
	return nil
}

// processAttachments 處理附件內容
// 支援文件、網頁內容和純文本，明確排除 YouTube 連結
func (c *Claude) processAttachments(ctx context.Context, attachments *model.Asset) error {
	// nil 檢查
	if attachments == nil {
		return nil
	}

	c.logger().Debug(ctx, "Processing attachments...")

	// Token 管理檢查
	err := c.checkAndManageTokens(ctx)
	if err != nil {
		return gerror.Wrap(err, "failed to check and manage tokens before processing attachments")
	}

	// 統計處理結果（garray.Array 內建線程安全，無需手動加鎖）
	processedCount := 0
	skippedCount := 0

	// 1. 處理 WebPageFiles (.md 格式的網頁內容文件)
	for _, webPageFile := range attachments.WebPageFiles {
		if g.IsEmpty(webPageFile) {
			continue
		}

		// 文件驗證和安全檢查
		if !gfile.Exists(webPageFile) {
			c.logger().Warningf(ctx, "WebPage file does not exist: %s", webPageFile)
			skippedCount++
			continue
		}
		if gfile.Size(webPageFile) == 0 {
			c.logger().Warningf(ctx, "WebPage file is empty: %s", webPageFile)
			skippedCount++
			continue
		}
		if gfile.Size(webPageFile) > 10*1024*1024 { // 10MB 限制
			c.logger().Warningf(ctx, "WebPage file too large (>10MB): %s", webPageFile)
			skippedCount++
			continue
		}

		// 讀取網頁文件內容
		content := gfile.GetContents(webPageFile)
		if g.IsEmpty(content) {
			c.logger().Warningf(ctx, "Failed to read WebPage file content: %s", webPageFile)
			skippedCount++
			continue
		}

		// 創建文本類型的 ClaudeMessage
		webPageMessage := claude.ClaudeMessage{
			Role: "user",
			Content: []claude.ContentPart{
				{
					Type: "text",
					Text: fmt.Sprintf("網頁內容文件 (%s):\n%s", gfile.Basename(webPageFile), content),
				},
			},
		}

		// 添加到對話歷史（線程安全）
		c.history.Append(webPageMessage)
		processedCount++

		c.logger().Debugf(ctx, "Processed WebPage file: %s", webPageFile)

		// 檢查 token 數量，如果接近閾值則停止處理剩餘附件
		// garray.Array 內建線程安全，無需手動釋放鎖
		tokenCount, tokenErr := c.calculateTokenCount(ctx)
		if tokenErr == nil && tokenCount > consts.ClaudeTokenThreshold*0.9 { // 90% 閾值
			c.logger().Warningf(ctx, "Token count approaching threshold, stopping attachment processing")
			break
		}
	}

	// 2. 處理 Files (區分圖片和非圖片)
	for _, filePath := range attachments.Files {
		if g.IsEmpty(filePath) {
			continue
		}

		// 文件驗證和安全檢查
		if !gfile.Exists(filePath) {
			c.logger().Warningf(ctx, "File does not exist: %s", filePath)
			skippedCount++
			continue
		}
		if gfile.Size(filePath) == 0 {
			c.logger().Warningf(ctx, "File is empty: %s", filePath)
			skippedCount++
			continue
		}
		if gfile.Size(filePath) > 10*1024*1024 { // 10MB 限制
			c.logger().Warningf(ctx, "File too large (>10MB): %s", filePath)
			skippedCount++
			continue
		}

		// 檢測 MIME 類型
		mimeType, err := mimetype.DetectFile(filePath)
		if err != nil {
			c.logger().Warningf(ctx, "Failed to detect MIME type for file %s: %v", filePath, err)
			skippedCount++
			continue
		}

		mimeTypeStr := mimeType.String()

		// 檢查是否為支援的圖片格式
		if c.isSupportedImageType(mimeTypeStr) {
			// 處理圖片文件
			imageData := gfile.GetBytes(filePath)
			if len(imageData) == 0 {
				c.logger().Warningf(ctx, "Failed to read image file: %s", filePath)
				skippedCount++
				continue
			}

			// 轉換為 base64
			base64Data := gbase64.EncodeToString(imageData)

			// 創建圖片類型的 ClaudeMessage
			imageMessage := claude.ClaudeMessage{
				Role: "user",
				Content: []claude.ContentPart{
					{
						Type: "image",
						Source: &claude.ImageSource{
							Type:      "base64",
							MediaType: mimeTypeStr,
							Data:      base64Data,
						},
					},
					{
						Type: "text",
						Text: fmt.Sprintf("圖片文件: %s", gfile.Basename(filePath)),
					},
				},
			}

			// 添加到對話歷史（線程安全）
			c.history.Append(imageMessage)
			processedCount++

			c.logger().Debugf(ctx, "Processed image file: %s (MIME: %s)", filePath, mimeTypeStr)
		} else {
			// 處理非圖片文件，嘗試使用 markitdown 轉換
			convertedPath, isConverted, shouldSkip, convErr := utility.ConvertFileToMarkdown(ctx, filePath, mimeTypeStr)
			if convErr != nil || shouldSkip {
				if convErr != nil {
					c.logger().Errorf(ctx, "Failed to convert file %s to markdown, skipping file: %v", filePath, convErr)
				}
				skippedCount++
				continue
			}

			// 讀取文件內容（轉換後的或原始的）
			content := gfile.GetContents(convertedPath)
			if g.IsEmpty(content) {
				c.logger().Warningf(ctx, "Failed to read file content or file is empty: %s", convertedPath)
				skippedCount++
				// 如果轉換成功但讀取失敗，清理臨時文件
				if isConverted && convertedPath != filePath {
					_ = gfile.RemoveFile(convertedPath)
				}
				continue
			}

			// 創建文本類型的 ClaudeMessage
			fileTypeDesc := mimeTypeStr
			if isConverted {
				fileTypeDesc = "markdown (converted from " + mimeTypeStr + ")"
			}

			fileMessage := claude.ClaudeMessage{
				Role: "user",
				Content: []claude.ContentPart{
					{
						Type: "text",
						Text: fmt.Sprintf("文件內容 (%s, MIME: %s):\n%s", gfile.Basename(filePath), fileTypeDesc, content),
					},
				},
			}

			// 添加到對話歷史（線程安全）
			c.history.Append(fileMessage)
			processedCount++

			c.logger().Debugf(ctx, "Processed text file: %s (MIME: %s, converted: %v)", filePath, mimeTypeStr, isConverted)

			// 如果轉換成功，清理臨時文件
			if isConverted && convertedPath != filePath {
				_ = gfile.RemoveFile(convertedPath)
			}
		}

		// 檢查 token 數量，如果接近閾值則停止處理剩餘附件
		// garray.Array 內建線程安全，無需手動釋放鎖
		tokenCount, tokenErr := c.calculateTokenCount(ctx)
		if tokenErr == nil && tokenCount > consts.ClaudeTokenThreshold*0.9 { // 90% 閾值
			c.logger().Warningf(ctx, "Token count approaching threshold, stopping attachment processing")
			break
		}
	}

	// 3. 處理 PlainText (純文本內容)
	for i, plainText := range attachments.PlainText {
		if g.IsEmpty(plainText) {
			continue
		}

		// 創建純文本類型的 ClaudeMessage
		plainTextMessage := claude.ClaudeMessage{
			Role: "user",
			Content: []claude.ContentPart{
				{
					Type: "text",
					Text: fmt.Sprintf("純文本內容 #%d:\n%s", i+1, plainText),
				},
			},
		}

		// 添加到對話歷史（線程安全）
		c.history.Append(plainTextMessage)
		processedCount++

		c.logger().Debugf(ctx, "Processed plain text #%d", i+1)

		// 檢查 token 數量，如果接近閾值則停止處理剩餘附件
		// garray.Array 內建線程安全，無需手動釋放鎖
		tokenCount, tokenErr := c.calculateTokenCount(ctx)
		if tokenErr == nil && tokenCount > consts.ClaudeTokenThreshold*0.9 { // 90% 閾值
			c.logger().Warningf(ctx, "Token count approaching threshold, stopping attachment processing")
			break
		}
	}

	// 記錄處理結果總結
	c.logger().Infof(ctx, "Attachment processing completed: %d processed, %d skipped",
		processedCount, skippedCount)

	return nil
}

// getSystemInstruction 獲取系統指令
// 支援動態日期替換和指令格式化
func (c *Claude) getSystemInstruction(ctx context.Context, payload *llm.Payload) string {
	if payload == nil {
		return ""
	}

	systemInstruction := payload.SystemInstruction
	if g.IsEmpty(systemInstruction) {
		return ""
	}

	// 替換動態日期
	systemInstruction = gstr.Replace(systemInstruction, "{{.now_date}}", gtime.Now().Format("Y-m-d"))

	c.logger().Debugf(ctx, "System instruction prepared: %s", systemInstruction)
	return systemInstruction
}

// calculateTokenCount 計算當前對話的 token 數量
// 用於判斷是否需要進行對話總結
func (c *Claude) calculateTokenCount(ctx context.Context) (int32, error) {
	// garray.Array 內建線程安全，無需手動加鎖

	// 簡單的 token 估算邏輯
	// 實際實現中可能需要更精確的計算方法
	var totalTokens int32 = 0

	// 獲取歷史記錄的副本進行計算
	historySlice := c.history.Slice()
	for _, item := range historySlice {
		if message, ok := item.(claude.ClaudeMessage); ok {
			for _, content := range message.Content {
				if content.Type == "text" {
					// 粗略估算：每個字符約 0.25 個 token
					totalTokens += int32(len(content.Text)) / 4
				}
			}
		}
	}

	// 添加基本開銷
	totalTokens += consts.ClaudeTokenOverhead + int32(c.history.Len())*consts.ClaudeMsgOverhead

	c.logger().Debugf(ctx, "Calculated token count: %d", totalTokens)
	return totalTokens, nil
}

// checkAndManageTokens 檢查並管理 token 數量
// 當超過閾值時自動觸發對話總結
func (c *Claude) checkAndManageTokens(ctx context.Context) error {
	tokenCount, err := c.calculateTokenCount(ctx)
	if err != nil {
		c.logger().Error(ctx, "Failed to calculate token count:", err)
		return err
	}

	if tokenCount > consts.ClaudeTokenThreshold {
		c.logger().Infof(ctx, "Token count (%d) exceeds threshold (%d), summarizing conversation",
			tokenCount, consts.ClaudeTokenThreshold)
		return c.summarizeConversation(ctx)
	}

	c.logger().Debugf(ctx, "Token count (%d) is within threshold (%d)",
		tokenCount, consts.ClaudeTokenThreshold)
	return nil
}

// summarizeConversation 總結對話內容
// 生成對話摘要並重置歷史記錄
func (c *Claude) summarizeConversation(ctx context.Context) error {
	// garray.Array 內建線程安全，無需手動加鎖

	if c.history.Len() == 0 {
		c.logger().Debug(ctx, "No conversation history to summarize")
		return nil
	}

	c.logger().Info(ctx, "Starting conversation summarization")

	// 創建總結提示
	summaryPrompt := "Please summarize our conversation so far in a concise manner, focusing on the key points and context. Use traditional Chinese for the summary."

	// 準備總結請求
	historySlice := c.history.Slice()
	summaryMessages := make([]claude.ClaudeMessage, 0, len(historySlice)+1)

	// 將歷史記錄轉換為 ClaudeMessage 切片
	for _, item := range historySlice {
		if message, ok := item.(claude.ClaudeMessage); ok {
			summaryMessages = append(summaryMessages, message)
		}
	}

	// 添加總結提示
	summaryMessages = append(summaryMessages, claude.ClaudeMessage{
		Role: "user",
		Content: []claude.ContentPart{
			{
				Type: "text",
				Text: summaryPrompt,
			},
		},
	})

	// 發送總結請求
	summaryResponse, err := c.sendClaudeRequest(ctx, summaryMessages, "", consts.ClaudeSummaryTemp)
	if err != nil {
		wrappedErr := gerror.WrapCode(consts.ApiFailed, err, "Failed to generate conversation summary")
		c.logger().Error(ctx, wrappedErr)
		return wrappedErr
	}

	// 提取總結內容
	var summary string
	if len(summaryResponse.Content) > 0 {
		summary = summaryResponse.Content[0].Text
	}

	if g.IsEmpty(summary) {
		err = gerror.New("Failed to generate summary: empty response")
		c.logger().Error(ctx, err)
		return err
	}

	c.logger().Debugf(ctx, "Generated conversation summary: %s", summary)

	// 重置歷史記錄，保留摘要（線程安全）
	c.history.Clear()
	summaryMessage := claude.ClaudeMessage{
		Role: "user",
		Content: []claude.ContentPart{
			{
				Type: "text",
				Text: fmt.Sprintf("Previous conversation summary: %s", summary),
			},
		},
	}
	c.history.Append(summaryMessage)

	c.logger().Info(ctx, "Conversation successfully summarized and history reset")
	return nil
}

// Chat 處理聊天消息並返回響應
// 包含完整的參數驗證、token 管理和錯誤處理
func (c *Claude) Chat(ctx context.Context, message *llm.Message) (response *llm.ResponseData, err error) {
	// 參數驗證
	if message == nil {
		err = gerror.New("message parameter cannot be nil")
		c.logger().Error(ctx, err)
		return nil, err
	}

	if g.IsEmpty(message.Content) {
		err = gerror.New("message content cannot be empty")
		c.logger().Error(ctx, err)
		return nil, err
	}

	c.logger().Infof(ctx, "Processing chat message: %v", gjson.New(message).MustToJsonIndentString())

	// 檢查並管理 token 數量
	err = c.checkAndManageTokens(ctx)
	if err != nil {
		c.logger().Error(ctx, "Failed to manage tokens:", err)
		return nil, err
	}

	// 初始化響應結構
	response = &llm.ResponseData{
		TotalTokenCount: 0,
	}

	// 根據內容類型處理消息
	var userMessage claude.ClaudeMessage
	switch message.ContentType {
	case consts.ContentTypeText:
		// 處理文本消息
		userMessage = claude.ClaudeMessage{
			Role: "user",
			Content: []claude.ContentPart{
				{
					Type: "text",
					Text: gconv.String(message.Content),
				},
			},
		}

	case consts.ContentMediaFile:
		// 處理媒體文件
		userMessage, err = c.processMediaMessage(ctx, message)
		if err != nil {
			c.logger().Error(ctx, "Failed to process media message:", err)
			return nil, err
		}

	default:
		err = gerror.Newf("Unsupported content type: %v", message.ContentType)
		c.logger().Error(ctx, err)
		return nil, err
	}

	// 獲取 AI 回應
	aiResponse, err := c.getResponse(ctx, userMessage)
	if err != nil {
		c.logger().Error(ctx, "Failed to get AI response:", err)
		return nil, err
	}

	response.Response = aiResponse.Content[0].Text
	response.TotalTokenCount = aiResponse.Usage.InputTokens + aiResponse.Usage.OutputTokens

	c.logger().Debugf(ctx, "Chat completed successfully, tokens used: %d", response.TotalTokenCount)
	return response, nil
}

// processMediaMessage 處理媒體消息
// 支援圖片等媒體文件的 base64 編碼處理
func (c *Claude) processMediaMessage(ctx context.Context, message *llm.Message) (claude.ClaudeMessage, error) {
	data := gconv.Bytes(message.Content)
	mimeType := message.MimeType

	if g.IsEmpty(mimeType) {
		mimeType = http.DetectContentType(data)
	}

	c.logger().Debugf(ctx, "Processing media message with MIME type: %s", mimeType)

	// 檢查是否為支援的圖片格式
	if !c.isSupportedImageType(mimeType) {
		return claude.ClaudeMessage{}, gerror.Newf("Unsupported media type: %s", mimeType)
	}

	// 將圖片數據編碼為 base64
	base64Data := gbase64.EncodeToString(data)

	userMessage := claude.ClaudeMessage{
		Role: "user",
		Content: []claude.ContentPart{
			{
				Type: "image",
				Source: &claude.ImageSource{
					Type:      "base64",
					MediaType: mimeType,
					Data:      base64Data,
				},
			},
			{
				Type: "text",
				Text: "請分析這個圖片",
			},
		},
	}

	return userMessage, nil
}

// isSupportedImageType 檢查是否為支援的圖片類型
func (c *Claude) isSupportedImageType(mimeType string) bool {
	supportedTypes := []string{
		"image/jpeg",
		"image/png",
		"image/gif",
		"image/webp",
	}

	for _, supportedType := range supportedTypes {
		if strings.EqualFold(mimeType, supportedType) {
			return true
		}
	}
	return false
}

// getResponse 處理用戶消息並生成 AI 回應
// 包含重試機制和完整的錯誤處理
func (c *Claude) getResponse(ctx context.Context, userMessage claude.ClaudeMessage) (*claude.ClaudeResponse, error) {
	c.logger().Debugf(ctx, "Start processing user message")

	// 線程安全地添加用戶消息到歷史記錄
	c.history.Append(userMessage)

	// 創建消息副本用於 API 調用（線程安全）
	historySlice := c.history.Slice()
	messagesCopy := make([]claude.ClaudeMessage, 0, len(historySlice))
	for _, item := range historySlice {
		if message, ok := item.(claude.ClaudeMessage); ok {
			messagesCopy = append(messagesCopy, message)
		}
	}

	// 獲取系統指令
	systemInstruction := c.getSystemInstruction(ctx, c.payload)

	// 發送請求並處理重試
	var claudeResponse *claude.ClaudeResponse
	var lastErr error

	for attempt := 1; attempt <= consts.ClaudeMaxRetryAttempts; attempt++ {
		claudeResponse, lastErr = c.sendClaudeRequest(ctx, messagesCopy, systemInstruction, c.temperature)

		if lastErr == nil {
			// 請求成功，跳出重試循環
			c.logger().Debugf(ctx, "API call succeeded on attempt %d", attempt)
			break
		}

		// 記錄錯誤並準備重試
		c.logger().Infof(ctx, "API call failed, attempt %d/%d: %v",
			attempt, consts.ClaudeMaxRetryAttempts, lastErr)

		// 檢查是否為認證錯誤，如果是則嘗試刷新 token
		if gerror.HasCode(lastErr, gcode.CodeNotAuthorized) && attempt < consts.ClaudeMaxRetryAttempts {
			c.logger().Info(ctx, "Detected authentication error, attempting to refresh token")

			// 嘗試刷新 token（從 llmsConfig 中獲取憑證文件路徑）
			if c.llmsConfig != nil && !g.IsEmpty(c.llmsConfig.Vertex.CredentialFile) {
				refreshErr := c.refreshAccessToken(ctx, c.llmsConfig.Vertex.CredentialFile)
				if refreshErr != nil {
					c.logger().Error(ctx, "Failed to refresh token:", refreshErr)
				} else {
					c.logger().Info(ctx, "Token refreshed, will retry request")
					// 刷新成功，繼續重試（不需要額外等待）
					continue
				}
			} else {
				c.logger().Warning(ctx, "Cannot refresh token: credential file path not available")
			}
		}

		// 如果不是最後一次嘗試，則等待後重試
		if attempt < consts.ClaudeMaxRetryAttempts {
			vTtl, _ := g.Cfg().Get(ctx, "system.ai_send_retry_ttl", "40s")
			time.Sleep(vTtl.Duration())
		}
	}

	// 如果所有嘗試都失敗，返回包裝後的錯誤
	if lastErr != nil {
		wrappedErr := gerror.WrapCode(consts.ApiFailed, lastErr,
			fmt.Sprintf("Failed to generate response after %d attempts", consts.ClaudeMaxRetryAttempts))
		c.logger().Error(ctx, wrappedErr)
		return nil, wrappedErr
	}

	// 驗證響應
	if claudeResponse == nil || len(claudeResponse.Content) == 0 {
		err := gerror.New("Failed to generate response: empty response from API")
		c.logger().Error(ctx, err)
		return nil, err
	}

	// 線程安全地將 AI 回覆添加到歷史記錄
	assistantMessage := claude.ClaudeMessage{
		Role: "assistant",
		Content: []claude.ContentPart{
			{
				Type: "text",
				Text: claudeResponse.Content[0].Text,
			},
		},
	}
	c.history.Append(assistantMessage)

	c.logger().Debugf(ctx, "Generated response successfully")
	return claudeResponse, nil
}

// sendClaudeRequest 發送請求到 Claude API
// 處理 HTTP 請求和響應解析
func (c *Claude) sendClaudeRequest(ctx context.Context, messages []claude.ClaudeMessage, systemInstruction string, temperature float32) (*claude.ClaudeResponse, error) {
	// 記錄方法開始時的請求參數
	systemInstructionLength := len(systemInstruction)
	c.logger().Infof(ctx, "Starting Claude API request - messages count: %d, system instruction length: %d, temperature: %.2f",
		len(messages), systemInstructionLength, temperature)

	// 構建請求體，符合 Google Vertex AI Claude API 規範
	request := claude.ClaudeRequest{
		AnthropicVersion: c.apiVersion, // 使用配置中的 API 版本
		MaxTokens:        c.maxOutputTokens,
		Temperature:      temperature,
		Messages:         messages,
		Stream:           false, // 非流式響應
	}

	// 添加系統指令（如果存在）
	if !g.IsEmpty(systemInstruction) {
		request.System = systemInstruction
	}

	// 序列化請求，使用自定義的 MarshalJSON 方法
	requestBytes, err := request.MarshalJSON()
	if err != nil {
		c.logger().Errorf(ctx, "Failed to marshal request: %v", err)
		return nil, gerror.Wrap(err, "failed to marshal request")
	}
	requestBody := string(requestBytes)

	// 記錄請求體內容以便調試（遮罩敏感資訊）
	c.logger().Debugf(ctx, "Request body: %s", requestBody)

	// 記錄請求 URL 和 headers（遮罩敏感資訊）
	maskedToken := ""
	if len(c.accessToken) > 10 {
		maskedToken = c.accessToken[:6] + "..." + c.accessToken[len(c.accessToken)-4:]
	} else {
		maskedToken = "***masked***"
	}
	c.logger().Infof(ctx, "Sending HTTP request - URL: %s, Authorization: Bearer %s, Content-Type: application/json",
		c.endpoint, maskedToken)

	// 設置請求頭
	c.httpClient.SetHeader("Authorization", fmt.Sprintf("Bearer %s", c.accessToken))
	// 設置代理，環境需要透過 proxy 連接外部 API
	vProxy, _ := g.Cfg().Get(ctx, "system.proxy")
	if vProxy != nil && !vProxy.IsEmpty() {

		c.httpClient.SetProxy(vProxy.String())
	}

	// 記錄請求開始時間
	startTime := gtime.TimestampMilli()

	// 發送 POST 請求
	response, err := c.httpClient.ContentJson().Post(ctx, c.endpoint, requestBody)

	// 計算響應時間
	responseTime := gtime.TimestampMilli() - startTime

	if err != nil {
		c.logger().Errorf(ctx, "HTTP request failed after %dms - error: %v", responseTime, err)
		return nil, gerror.Wrap(err, "failed to send HTTP request")
	}
	defer response.Close()

	// 記錄 HTTP 響應狀態碼和響應時間
	statusCode := response.StatusCode
	c.logger().Infof(ctx, "Received HTTP response - status code: %d, response time: %dms", statusCode, responseTime)

	// 讀取響應內容
	responseBody := response.ReadAllString()
	if responseBody == "" {
		c.logger().Errorf(ctx, "Empty response body received - status code: %d", statusCode)
		return nil, gerror.New("failed to send HTTP request: empty response")
	}

	// 檢查 HTTP 狀態碼
	if statusCode != 200 {
		c.logger().Errorf(ctx, "API request failed - status code: %d, response body: %s", statusCode, responseBody)

		// 檢查是否為認證相關錯誤（401 Unauthorized 或 403 Forbidden）
		if statusCode == 401 || statusCode == 403 {
			c.logger().Warningf(ctx, "Authentication error detected (status: %d), token may be expired", statusCode)
			return nil, gerror.NewCodef(gcode.CodeNotAuthorized, "Authentication failed with status code %d: %s", statusCode, responseBody)
		}

		return nil, gerror.Newf("API request failed with status code %d: %s", statusCode, responseBody)
	}

	// 解析響應
	responseJson := gjson.New(responseBody)
	var claudeResponse claude.ClaudeResponse
	err = responseJson.Scan(&claudeResponse)
	if err != nil {
		c.logger().Errorf(ctx, "Failed to parse response JSON - error: %v, response body: %s", err, responseBody)
		return nil, gerror.Wrap(err, "failed to unmarshal response")
	}

	// 記錄成功結果
	c.logger().Infof(ctx, "Claude API request completed successfully - tokens used: input=%d, output=%d, model=%s, stop_reason=%s",
		claudeResponse.Usage.InputTokens, claudeResponse.Usage.OutputTokens, claudeResponse.Model, claudeResponse.StopReason)

	return &claudeResponse, nil
}

// GenerateContent 統一的內容生成接口，支援智能續寫和完整響應處理
// 實現跨模型一致的內容生成功能
func (c *Claude) GenerateContent(ctx context.Context, request *llm.GenerateContentRequest) (*llm.GenerateContentResponse, error) {
	startTime := gtime.TimestampMilli()

	// 參數驗證
	if request == nil {
		err := gerror.New("request cannot be nil")
		c.logger().Error(ctx, err)
		return nil, err
	}

	if g.IsEmpty(request.Prompt) {
		err := gerror.New("prompt cannot be empty")
		c.logger().Error(ctx, err)
		return nil, err
	}

	// 檢查 HTTP 客戶端是否已初始化
	if c.httpClient == nil {
		err := gerror.New("claude http client is not initialized")
		c.logger().Error(ctx, err)
		return nil, err
	}

	// 應用預設值
	c.applyDefaults(ctx, request)

	// 記錄開始生成的日誌
	c.logger().Infof(ctx, "Starting Claude GenerateContent: prompt_length=%d, max_continuations=%d, token_budget=%d",
		len(request.Prompt), request.MaxContinuations, request.TotalTokenBudget)

	// 初始化響應結構
	response := &llm.GenerateContentResponse{
		LLMName:           c.modelName,
		InputContent:      request.Prompt,
		ContinuationCount: 0,
		IsComplete:        false,
		SafetyWarnings:    make([]string, 0),
	}

	// 執行智能續寫生成
	err := c.executeGenerationWithContinuation(ctx, request, response)
	if err != nil {
		return nil, err
	}

	// 計算生成時間
	response.GenerationTime = gtime.TimestampMilli() - startTime

	// 記錄完成日誌
	c.logger().Infof(ctx, "Claude GenerateContent completed: model=%s, continuations=%d/%d, input_tokens=%d, output_tokens=%d, total_tokens=%d, complete=%v, duration=%dms",
		response.LLMName, response.ContinuationCount, request.MaxContinuations,
		response.InputTokens, response.OutputTokens, response.TotalTokens,
		response.IsComplete, response.GenerationTime)

	return response, nil
}

// applyDefaults 應用預設配置值
func (c *Claude) applyDefaults(ctx context.Context, request *llm.GenerateContentRequest) {
	if request.MaxContinuations <= 0 {
		defaultValue, _ := g.Cfg().Get(ctx, "llms.claude.max_continuations", 3)
		request.MaxContinuations = gconv.Int(defaultValue)
	}

	if request.TotalTokenBudget <= 0 {
		defaultValue, _ := g.Cfg().Get(ctx, "llms.claude.total_token_budget", c.maxOutputTokens*2)
		request.TotalTokenBudget = gconv.Int32(defaultValue)
	}

	if request.Temperature == nil {
		request.Temperature = &c.temperature
	}
}

// executeGenerationWithContinuation 執行智能續寫生成
func (c *Claude) executeGenerationWithContinuation(ctx context.Context, request *llm.GenerateContentRequest, response *llm.GenerateContentResponse) error {
	var allContent strings.Builder
	var totalInputTokens, totalOutputTokens int32
	continuationCount := 0

	for continuationCount <= request.MaxContinuations {
		// 構建當前請求的 prompt
		currentPrompt := request.Prompt
		if continuationCount > 0 {
			currentPrompt = allContent.String() + "\n\n[請繼續完成上述內容]"
		}

		// 檢查 token 預算
		if totalInputTokens+totalOutputTokens >= request.TotalTokenBudget {
			c.logger().Debugf(ctx, "Token budget exceeded: %d/%d", totalInputTokens+totalOutputTokens, request.TotalTokenBudget)
			break
		}

		// 調用 Claude API
		claudeResponse, err := c.callClaudeAPI(ctx, currentPrompt, request)
		if err != nil {
			return err
		}

		// 累加內容和 token
		if len(claudeResponse.Content) > 0 {
			allContent.WriteString(claudeResponse.Content[0].Text)
		}
		totalInputTokens += claudeResponse.Usage.InputTokens
		totalOutputTokens += claudeResponse.Usage.OutputTokens

		// 保存完成原因
		response.FinishReason = claudeResponse.StopReason

		// 檢測是否需要續寫
		isComplete, reason := c.isContentComplete(allContent.String())

		c.logger().Debugf(ctx, "Claude Continuation %d: reason=%s, tokens_used=%d/%d, complete=%v",
			continuationCount, reason, totalInputTokens+totalOutputTokens, request.TotalTokenBudget, isComplete)

		if isComplete {
			response.IsComplete = true
			break
		}

		continuationCount++
	}

	// 設置最終響應數據
	response.OutputContent = allContent.String()
	response.InputTokens = totalInputTokens
	response.OutputTokens = totalOutputTokens
	response.TotalTokens = totalInputTokens + totalOutputTokens
	response.ContinuationCount = continuationCount

	return nil
}

// isContentComplete 檢測內容是否完整
func (c *Claude) isContentComplete(content string) (bool, string) {
	// 檢測未完成的程式碼區塊
	if gstr.Count(content, "```")%2 != 0 {
		return false, "incomplete_code_block"
	}

	// 檢測句子是否在合適位置結束
	trimmed := gstr.Trim(content)
	if g.IsEmpty(trimmed) {
		return false, "empty_content"
	}

	// 檢測常見的未完成標記
	incompletePatterns := []string{"...", "待續", "to be continued", "（未完）", "未完待續", "continue", "more", "接下來"}
	for _, pattern := range incompletePatterns {
		if gstr.ContainsI(content, pattern) {
			return false, "explicit_incomplete_marker"
		}
	}

	// 檢測是否以不完整的句子結尾
	lastChar := gstr.SubStrRune(trimmed, -1, 1)
	if !gstr.InArray([]string{".", "。", "!", "！", "?", "？", "}", "]", ")", "\"", "'", "`", ":", "：", ";", "；"}, lastChar) {
		// 如果內容較短，可能是正常結束
		if len(trimmed) < 50 {
			return true, "short_content_complete"
		}
		return false, "incomplete_sentence"
	}

	return true, "complete"
}

// callClaudeAPI 調用 Claude API
func (c *Claude) callClaudeAPI(ctx context.Context, prompt string, request *llm.GenerateContentRequest) (*claude.ClaudeResponse, error) {
	// 構建 Claude 消息格式（不修改實例的歷史記錄）
	messages := make([]claude.ClaudeMessage, 0)

	// 添加用戶消息
	messages = append(messages, claude.ClaudeMessage{
		Role: "user",
		Content: []claude.ContentPart{
			{
				Type: "text",
				Text: prompt,
			},
		},
	})

	// 設置生成選項
	temperature := c.temperature
	if request.Temperature != nil {
		temperature = *request.Temperature
	}

	// 構建系統指令
	systemInstruction := request.SystemInstruction
	if g.IsEmpty(systemInstruction) {
		systemInstruction = "" // Claude 可以接受空的系統指令
	}

	var claudeResponse *claude.ClaudeResponse
	var lastErr error

	// 實現重試機制，與現有 getResponse 方法保持一致
	for attempt := 1; attempt <= consts.ClaudeMaxRetryAttempts; attempt++ {
		claudeResponse, lastErr = c.sendClaudeRequest(ctx, messages, systemInstruction, temperature)

		if lastErr == nil {
			c.logger().Debugf(ctx, "Claude API call succeeded on attempt %d", attempt)
			break
		}

		// 記錄錯誤並準備重試
		c.logger().Infof(ctx, "Claude API call failed, attempt %d/%d: %v",
			attempt, consts.ClaudeMaxRetryAttempts, lastErr)

		// 檢查是否為認證錯誤，如果是則嘗試刷新 token
		if gerror.HasCode(lastErr, gcode.CodeNotAuthorized) && attempt < consts.ClaudeMaxRetryAttempts {
			c.logger().Info(ctx, "Detected authentication error in GenerateContent, attempting to refresh token")

			// 嘗試刷新 token（從 llmsConfig 中獲取憑證文件路徑）
			if c.llmsConfig != nil && !g.IsEmpty(c.llmsConfig.Vertex.CredentialFile) {
				refreshErr := c.refreshAccessToken(ctx, c.llmsConfig.Vertex.CredentialFile)
				if refreshErr != nil {
					c.logger().Error(ctx, "Failed to refresh token:", refreshErr)
				} else {
					c.logger().Info(ctx, "Token refreshed, will retry GenerateContent request")
					// 刷新成功，繼續重試（不需要額外等待）
					continue
				}
			} else {
				c.logger().Warning(ctx, "Cannot refresh token: credential file path not available")
			}
		}

		// 如果不是最後一次嘗試，則等待後重試
		if attempt < consts.ClaudeMaxRetryAttempts {
			vTtl, _ := g.Cfg().Get(ctx, "system.ai_send_retry_ttl", "40s")
			time.Sleep(vTtl.Duration())
		}
	}

	if lastErr != nil {
		return nil, gerror.WrapCode(consts.ApiFailed, lastErr, "failed to call Claude API after retries")
	}

	// 驗證響應
	if claudeResponse == nil || len(claudeResponse.Content) == 0 {
		return nil, gerror.New("empty response from Claude API")
	}

	return claudeResponse, nil
}

// GenerateContentSimple 簡化版本，向後兼容
func (c *Claude) GenerateContentSimple(ctx context.Context, prompt string) (string, error) {
	request := &llm.GenerateContentRequest{
		Prompt:           prompt,
		MaxContinuations: 3,
		TotalTokenBudget: c.maxOutputTokens * 2,
		IncludeThinking:  false,
	}

	response, err := c.GenerateContent(ctx, request)
	if err != nil {
		return "", err
	}

	return response.OutputContent, nil
}

// Release 釋放 Claude 服務資源
// 清理對話歷史記錄和相關資源
func (c *Claude) Release(ctx context.Context) {
	c.logger().Debug(ctx, "Releasing Claude service resources")

	// 清理對話歷史記錄（線程安全）
	if c.history != nil {
		c.history.Clear()
		c.history = nil
	}

	// 清理 HTTP 客戶端
	if c.httpClient != nil {
		// GoFrame 客戶端會自動管理連接池，無需手動關閉
		c.httpClient = nil
	}

	// 重置其他欄位
	c.endpoint = ""
	c.accessToken = ""
	c.projectID = ""
	c.region = ""
	c.modelName = ""
	c.temperature = 0
	c.maxOutputTokens = 0
	c.payload = nil
	c.llmsConfig = nil

	c.logger().Info(ctx, "Claude service resources released successfully")
}
