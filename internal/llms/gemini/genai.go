package gemini

import (
	"brainHub/internal/consts"
	"brainHub/internal/llms"
	"brainHub/internal/model/llm"
	"brainHub/utility"
	"context"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"github.com/gabriel-vasile/mimetype"
	"github.com/gogf/gf/v2/container/garray"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"google.golang.org/genai"
)

// ================================================================================================
// 常數定義區域
// ================================================================================================

const (
	// API 調用相關常數
	defaultRetryCount       = 3     // 預設重試次數
	defaultRetryDelayConfig = "40s" // 預設重試延遲配置鍵
	defaultClientTimeout    = 60    // HTTP 客戶端超時時間（秒）
	defaultThinkingBudget   = 1000  // 預設思考預算
	defaultMaxContinuations = 3     // 預設最大續寫次數

	// MIME 類型常數
	mimeTypeJSON     = "application/json"
	mimeTypeMarkdown = "text/markdown"
	mimeTypeMD       = "text/md"

	// 配置鍵常數
	configKeyProxyURL         = "system.proxy"
	configKeyVertexKeyPath    = "system.vertex_key_path"
	configKeyRetryTTL         = "system.ai_send_retry_ttl"
	configKeyMaxContinuations = "llms.gemini.max_continuations"
	configKeyTotalTokenBudget = "llms.gemini.total_token_budget"

	// 環境變數常數
	envGoogleApplicationCredentials = "GOOGLE_APPLICATION_CREDENTIALS"
)

// 內容完整性檢測模式
var incompleteContentPatterns = []string{
	"...",
	"待續",
	"to be continued",
	"（未完）",
	"未完待續",
}

// ================================================================================================
// 結構體定義區域
// ================================================================================================

// Gemini Google Gemini AI 服務實現
// 透過 Google Vertex AI 提供安全、高效能的 AI 對話功能
type Gemini struct {
	client          *genai.Client                // GoFrame HTTP 客戶端
	chat            *genai.Chat                  // 聊天會話實例，用於維護對話上下文
	config          *genai.GenerateContentConfig // 內容生成配置，包含溫度、token 限制等參數
	modelName       string                       // 使用的模型名稱（如 gemini-2.5-flash-preview）
	temperature     float32                      // 生成溫度參數，控制輸出的隨機性
	maxOutputTokens int32                        // 最大輸出 token 數量限制
	projectID       string                       // GCP 項目 ID
	region          string                       // GCP 區域
	history         *garray.Array                // 對話歷史記錄（線程安全），使用 garray.Array 避免死鎖問題
	payload         *llm.Payload                 // 初始化載荷
	llmsConfig      *llm.LLMsConfig              // LLM 配置，用於重新初始化
}

// ================================================================================================
// 工廠方法和基礎方法
// ================================================================================================

// New 創建新的 Gemini 服務實例
// 返回實現 ILLMs 接口的實例
func New() llms.ILLMs {
	return &Gemini{
		history: garray.New(true), // 初始化線程安全的 garray
	}
}

// logger 返回專用的日誌記錄器
func (g *Gemini) logger() glog.ILogger {
	return g.Log().Cat(consts.CatalogGemini)
}

// ================================================================================================
// ILLMs 接口實現方法
// ================================================================================================

// Initialize 初始化 Gemini LLM 實例
// 設置客戶端連接、配置參數、認證憑證等
// 支援 Vertex AI 後端、代理配置和智能認證域名處理
// 參數：
//   - ctx: 上下文對象，用於控制請求生命週期
//   - params: LLM 配置參數，包含 Vertex AI 設置和模型參數
//   - payload: 會話負載數據，包含系統指令、附件和歷史記錄
//
// 返回：
//   - error: 初始化過程中的錯誤信息
func (m *GeminiLLM) Initialize(ctx context.Context, params *llm.LLMsConfig, payload *llm.Payload) (err error) {
	m.logger().Debugf(ctx, "Starting Gemini LLM initialization with parameters: %v",
		gjson.New(params).MustToJsonIndentString())

	// 參數驗證：確保必要的配置參數不為空
	if params == nil {
		err = gerror.New("initialization parameters cannot be nil")
		m.logger().Error(ctx, err)
		return err
	}

	// 檢查是否已經初始化，避免重複初始化
	if m.client != nil && m.chat != nil && m.config != nil {
		m.logger().Info(ctx, "Gemini client is already initialized, skipping re-initialization")
		return nil
	}

	// 設置 Google 認證憑證（簡化版本，移除 NO_PROXY 操作）
	if err = m.setupGoogleCredentialsSimple(ctx, params); err != nil {
		err = gerror.WrapCode(consts.ApiFailed, err, "failed to setup Google credentials")
		m.logger().Error(ctx, err)
		return err
	}

	// 創建客戶端配置
	// 確保區域設置正確，如果配置為空則使用預設值
	// region := params.Vertex.Region
	region := ""
	if region == "" {
		region = "us-central1" // 預設區域
		m.logger().Warningf(ctx, "Region not configured, using default: %s", region)
	}

	// 創建客戶端配置（簡化配置，參考成功範例）
	m.logger().Debugf(ctx, "🚀 Creating Gemini client configuration...")
	clientConfig := &genai.ClientConfig{
		Project:  params.Vertex.ProjectID,
		Location: region,
		Backend:  genai.BackendVertexAI,
	}

	// 記錄客戶端配置信息
	m.logger().Infof(ctx, "🚀 Creating Gemini client with config - Project: %s, Location: %s, Backend: VertexAI",
		params.Vertex.ProjectID, region)

	// 設置代理配置（如果需要）
	m.logger().Debugf(ctx, "🌐 Setting up proxy configuration...")
	if err = m.setupProxyConfiguration(ctx, clientConfig); err != nil {
		m.logger().Errorf(ctx, "❌ Proxy configuration failed: %v", err)
		return err
	}

	// 創建 Gemini 客戶端
	m.logger().Debugf(ctx, "🚀 Creating Gemini client instance...")
	m.client, err = genai.NewClient(ctx, clientConfig)
	if err != nil {
		err = gerror.WrapCode(consts.ApiFailed, err, "failed to create Gemini client")
		m.logger().Errorf(ctx, "❌ Failed to create Gemini client: %v", err)
		return err
	}
	m.logger().Infof(ctx, "✅ Gemini client created successfully")

	// 保存配置參數到實例變數（在驗證授權之前設置，確保 modelName 可用）
	m.temperature = params.Vertex.Gemini.Temperature
	m.maxOutputTokens = params.Vertex.Gemini.MaxOutputTokens
	m.modelName = params.Vertex.Gemini.Model
	m.payload = payload

	// 跳過初始化時的授權驗證，改為在實際使用時進行驗證
	// 這樣可以避免初始化階段的授權問題，提高系統穩定性
	m.logger().Infof(ctx, "Gemini client initialized successfully, authentication will be validated on first use")

	// 設置內容生成配置
	m.config = &genai.GenerateContentConfig{
		Temperature:      genai.Ptr(params.Vertex.Gemini.Temperature),
		MaxOutputTokens:  params.Vertex.Gemini.MaxOutputTokens,
		ResponseMIMEType: mimeTypeJSON,
		ThinkingConfig: &genai.ThinkingConfig{
			IncludeThoughts: params.Vertex.Gemini.IncludeThoughts,
			ThinkingBudget:  genai.Ptr(params.Vertex.Gemini.ThinkingBudget),
		},
	}

	// 創建新的聊天會話
	err = m.createNewChat(ctx, payload, nil)
	if err != nil {
		return err
	}

	m.logger().Infof(ctx, "Gemini LLM initialization completed successfully with model: %s", m.modelName)
	return nil
}

// createNewChat 創建新的聊天會話
// 處理系統指令、附件文件、歷史記錄等內容，建立完整的對話上下文
// 支援多種附件類型：網頁文件、普通文件、純文本、YouTube 連結等
// 參數：
//   - ctx: 上下文對象
//   - payload: 會話負載數據，包含系統指令和附件
//   - dialogSummary: 可選的對話摘要內容，用於長對話的上下文壓縮
//
// 返回：
//   - error: 創建過程中的錯誤信息
func (m *GeminiLLM) createNewChat(ctx context.Context, payload *llm.Payload, dialogSummary *genai.Content) (err error) {
	m.logger().Debugf(ctx, "Creating new chat session with payload: %v",
		gjson.New(payload).MustToJsonIndentString())

	// 處理系統指令
	systemInstruction := ""
	if payload != nil {
		systemInstruction = payload.SystemInstruction
	}

	// 替換系統指令中的動態變數（如當前日期）
	systemInstruction = gstr.Replace(systemInstruction, "{{.now_date}}", gtime.Now().Format("Y-m-d"))

	// 設置系統指令到配置中
	if !g.IsEmpty(systemInstruction) {
		m.config.SystemInstruction = genai.NewContentFromText(systemInstruction, genai.RoleUser)
		m.logger().Debugf(ctx, "System instruction set: %s", systemInstruction)
	}

	var contents []*genai.Content

	// 處理附件內容：包含網頁文件、普通文件、純文本、YouTube 連結等
	if payload != nil && payload.Attachments != nil {
		// 處理網頁 Markdown 文件
		for _, webPageMDFile := range payload.Attachments.WebPageFiles {
			if gfile.Exists(webPageMDFile) && gfile.Size(webPageMDFile) > 0 {
				m.logger().Debugf(ctx, "Loading webpage file: %v", webPageMDFile)
				buf := gfile.GetBytes(webPageMDFile)
				contents = append(contents, genai.NewContentFromBytes(buf, mimeTypeMD, genai.RoleUser))
			}
		}

		// 處理普通文件附件
		processedFiles := 0
		skippedFiles := 0
		for _, file := range payload.Attachments.Files {
			if !gfile.Exists(file) || gfile.Size(file) == 0 {
				m.logger().Warningf(ctx, "File does not exist or is empty: %v", file)
				skippedFiles++
				continue
			}

			m.logger().Debugf(ctx, "Processing file: %v", file)

			// 檢測文件 MIME 類型
			mType := ""
			if mimeType, e := mimetype.DetectFile(file); e != nil {
				m.logger().Errorf(ctx, "Failed to detect MIME type for file %s: %v", file, e)
				skippedFiles++
				continue
			} else {
				mType = mimeType.String()
			}

			// 嘗試使用 markitdown 轉換文件為 Markdown 格式
			convertedPath, isConverted, shouldSkip, convErr := utility.ConvertFileToMarkdown(ctx, file, mType)
			if convErr != nil || shouldSkip {
				if convErr != nil {
					m.logger().Errorf(ctx, "Failed to convert file %s to markdown, skipping: %v", file, convErr)
				}
				skippedFiles++
				continue
			}

			// 確定最終的 MIME 類型
			finalMimeType := mType
			if isConverted {
				finalMimeType = mimeTypeMarkdown
				// 設置延遲清理轉換生成的臨時文件
				defer func(path string) {
					if path != file { // 只清理轉換生成的文件，不清理原始文件
						if err := gfile.RemoveFile(path); err != nil {
							m.logger().Warningf(ctx, "Failed to clean up temporary file %s: %v", path, err)
						}
					}
				}(convertedPath)
			}

			// 將文件內容添加到聊天上下文
			fileContent := gfile.GetBytes(convertedPath)
			contents = append(contents, genai.NewContentFromBytes(fileContent, finalMimeType, genai.RoleUser))
			processedFiles++
		}

		// 記錄文件處理結果
		if len(payload.Attachments.Files) > 0 {
			m.logger().Infof(ctx, "File attachment processing completed: processed=%d, skipped=%d, total=%d",
				processedFiles, skippedFiles, len(payload.Attachments.Files))
		}
		// 處理純文本附件
		for i, plainText := range payload.Attachments.PlainText {
			if !g.IsEmpty(plainText) {
				contents = append(contents, genai.NewContentFromText(plainText, genai.RoleUser))
				m.logger().Debugf(ctx, "Added plain text attachment %d", i)
			}
		}

		// 處理 YouTube 連結附件（目前只支援第一個連結）
		if len(payload.Attachments.YoutubeLink) > 0 {
			ytLink := payload.Attachments.YoutubeLink[0]
			if !g.IsEmpty(ytLink) {
				mime := utility.GetYTMime(ctx, ytLink)
				contents = append(contents, genai.NewContentFromURI(ytLink, mime, genai.RoleUser))
				m.logger().Debugf(ctx, "Added YouTube link: %s with MIME type: %s", ytLink, mime)
			}
		}

		// 處理歷史對話記錄
		if payload.History != nil {
			historyMessages := gconv.Strings(payload.History)
			for i, message := range historyMessages {
				if !g.IsEmpty(message) {
					contents = append(contents, genai.NewContentFromText(message, genai.RoleUser))
					m.logger().Debugf(ctx, "Added history message %d", i)
				}
			}
		}
	}
	// 添加前次對話的總結（如果存在）
	if dialogSummary != nil {
		contents = append(contents, dialogSummary)
		m.logger().Debugf(ctx, "Added dialog summary to chat context")
	}

	// 創建聊天會話
	m.chat, err = m.client.Chats.Create(ctx, m.modelName, m.config, contents)
	if err != nil {
		err = gerror.WrapCode(consts.ApiFailed, err, "failed to create chat session")
		m.logger().Error(ctx, err)
		return err
	}

	m.logger().Infof(ctx, "Chat session created successfully with %d content items", len(contents))
	return nil
}

func (m *GeminiLLM) summarized(ctx context.Context) (content *genai.Content, err error) {
	history := m.chat.History(false)

	var parts []genai.Part
	for _, content := range history {
		for _, part := range content.Parts {
			parts = append(parts, *part)
		}
	}
	r, err := m.client.Models.GenerateContent(ctx, m.modelName, []*genai.Content{{
		Parts: []*genai.Part{
			{Text: "Please use traditional Chinese to summarize the above dialogue content in a concise manner"},
		},
		Role: genai.RoleUser,
	}},
		&genai.GenerateContentConfig{
			MaxOutputTokens: m.maxOutputTokens,
			Temperature:     genai.Ptr(m.temperature),
		})

	if err != nil {
		m.logger().Error(ctx, err)
		return nil, gerror.WrapCode(consts.ApiFailed, err)
	}

	if r != nil && len(r.Candidates) > 0 {
		summary := r.Candidates[0].Content.Parts[0].Text
		m.logger().Debugf(ctx, "summary: %v", summary)
		content = genai.NewContentFromText(summary, genai.RoleUser)
	} else {
		err = gerror.New("failed to generate summary")
		m.logger().Error(ctx, err)
	}

	return content, err
}

// Chat 處理聊天對話請求
// 支援文本消息、文件附件、智能續寫等功能
// 自動處理 token 限制、對話摘要、內容完整性檢測等
// 參數：
//   - ctx: 上下文對象，用於控制請求生命週期
//   - message: 用戶消息，包含文本內容和可選的附件
//
// 返回：
//   - response: AI 回應數據，包含生成的文本和元數據
//   - error: 處理過程中的錯誤信息
func (m *GeminiLLM) Chat(ctx context.Context, message *llm.Message) (response *llm.ResponseData, err error) {
	m.logger().Infof(ctx, "Processing chat message: %v", gjson.New(message).MustToJsonIndentString())

	// 驗證輸入參數
	if message == nil {
		err = gerror.New("chat message cannot be nil")
		m.logger().Error(ctx, err)
		return nil, err
	}

	// 驗證客戶端和聊天會話是否已初始化
	if m.client == nil || m.chat == nil {
		err = gerror.New("Gemini client or chat session is not initialized")
		m.logger().Error(ctx, err)
		return nil, err
	}

	// 智能 token 管理：檢查對話歷史長度，必要時進行摘要
	if m.chat != nil && m.payload != nil {
		history := m.chat.History(false)
		if len(history) > 0 {
			// 使用正確的 CountTokens 方法調用（參考成功範例，第四個參數為 nil）
			tokens, tokenErr := m.client.Models.CountTokens(ctx, m.modelName, history, nil)
			if tokenErr != nil {
				m.logger().Errorf(ctx, "Failed to count tokens: %v", tokenErr)
			} else if tokens != nil {
				m.logger().Debugf(ctx, "Current conversation tokens: %d", tokens.TotalTokens)

				// 如果 token 數量超過限制，進行對話摘要並重新創建聊天會話
				if tokens.TotalTokens > consts.GeminiMaxTokens {
					m.logger().Infof(ctx, "Token limit exceeded (%d > %d), summarizing conversation",
						tokens.TotalTokens, consts.GeminiMaxTokens)

					summaryContent, summaryErr := m.summarized(ctx)
					if summaryErr != nil {
						m.logger().Warningf(ctx, "Failed to summarize conversation: %v", summaryErr)
					}

					if createErr := m.createNewChat(ctx, m.payload, summaryContent); createErr != nil {
						m.logger().Errorf(ctx, "Failed to create new chat after summarization: %v", createErr)
						return nil, createErr
					}
				}
			}
		}
	}

	// 根據消息類型處理內容
	var content *genai.Content
	switch message.ContentType {
	case consts.ContentTypeText:
		// 處理純文本消息
		textContent := gconv.String(message.Content)
		if g.IsEmpty(textContent) {
			err = gerror.New("text content cannot be empty")
			m.logger().Error(ctx, err)
			return nil, err
		}
		content = genai.NewContentFromText(textContent, genai.RoleUser)
		m.logger().Debugf(ctx, "Processing text message with %d characters", len(textContent))

	case consts.ContentMediaFile:
		// 處理媒體文件消息
		data := gconv.Bytes(message.Content)
		if len(data) == 0 {
			err = gerror.New("media file content cannot be empty")
			m.logger().Error(ctx, err)
			return nil, err
		}

		// 確定 MIME 類型
		mimeType := message.MimeType
		if g.IsEmpty(mimeType) {
			mimeType = http.DetectContentType(data)
		}
		m.logger().Debugf(ctx, "Processing media file with MIME type: %s, size: %d bytes", mimeType, len(data))

		// 根據對話歷史決定處理方式
		if m.chat.History(false) != nil && len(m.chat.History(false)) == 0 {
			// 如果是對話的第一個消息，添加分析提示
			content = &genai.Content{
				Parts: []*genai.Part{
					{
						InlineData: &genai.Blob{
							Data:     data,
							MIMEType: mimeType,
						},
					},
					{
						Text: "分析這個文件",
					},
				},
				Role: genai.RoleUser,
			}
		} else {
			// 後續消息直接處理文件內容
			content = genai.NewContentFromBytes(data, mimeType, genai.RoleUser)
		}

	default:
		// 不支援的內容類型
		err = gerror.Newf("unsupported content type: %v", message.ContentType)
		m.logger().Error(ctx, err)
		return nil, err
	}
	// 提取消息部分用於發送
	var parts []genai.Part
	for _, part := range content.Parts {
		parts = append(parts, *part)
	}

	// 發送消息並處理重試邏輯
	var r *genai.GenerateContentResponse
	for i := 0; i < defaultRetryCount; i++ {
		r, err = m.chat.SendMessage(ctx, parts...)

		if err != nil {
			m.logger().Errorf(ctx, "Chat message send attempt %d failed: %v", i+1, err)

			// 如果不是最後一次重試，等待後重試
			if i < defaultRetryCount-1 {
				retryDelay, _ := g.Cfg().Get(ctx, configKeyRetryTTL, defaultRetryDelayConfig)
				sleepDuration := retryDelay.Duration()
				m.logger().Debugf(ctx, "Retrying in %v...", sleepDuration)
				time.Sleep(sleepDuration)
			}
		} else {
			m.logger().Debugf(ctx, "Chat message sent successfully on attempt %d", i+1)
			break
		}
	}

	// 處理響應結果
	if r != nil && len(r.Candidates) > 0 && len(r.Candidates[0].Content.Parts) > 0 {
		response = &llm.ResponseData{
			Response:        r.Candidates[0].Content.Parts[0].Text,
			TotalTokenCount: r.UsageMetadata.TotalTokenCount,
		}
		m.logger().Debugf(ctx, "Chat response generated successfully, tokens used: %d",
			response.TotalTokenCount)
	} else {
		err = gerror.New("received empty or invalid response from Gemini API")
		m.logger().Error(ctx, err)
		return nil, err
	}

	return response, nil
}

// GenerateContent 統一的內容生成接口
// 支援智能續寫、完整響應處理、多輪對話等高級功能
// 實現跨模型一致的內容生成功能，提供統一的 API 接口
// 參數：
//   - ctx: 上下文對象，用於控制請求生命週期
//   - request: 內容生成請求，包含提示詞、配置參數等
//
// 返回：
//   - response: 生成的內容響應，包含文本、token 統計等信息
//   - error: 處理過程中的錯誤信息
func (m *GeminiLLM) GenerateContent(ctx context.Context, request *llm.GenerateContentRequest) (*llm.GenerateContentResponse, error) {
	startTime := gtime.TimestampMilli()
	m.logger().Debugf(ctx, "Starting content generation with request: %v", gjson.New(request).MustToJsonIndentString())

	// 嚴格的參數驗證
	if request == nil {
		err := gerror.New("content generation request cannot be nil")
		m.logger().Error(ctx, err)
		return nil, err
	}

	if g.IsEmpty(request.Prompt) {
		err := gerror.New("prompt cannot be empty")
		m.logger().Error(ctx, err)
		return nil, err
	}

	// 檢查客戶端初始化狀態
	if m.client == nil {
		err := gerror.New("Gemini client is not initialized, please call Initialize() first")
		m.logger().Error(ctx, err)
		return nil, err
	}

	// 應用預設值
	m.applyDefaults(ctx, request)

	// 記錄開始生成的日誌
	m.logger().Infof(ctx, "Starting GenerateContent: prompt_length=%d, max_continuations=%d, token_budget=%d",
		len(request.Prompt), request.MaxContinuations, request.TotalTokenBudget)

	// 初始化響應結構
	response := &llm.GenerateContentResponse{
		LLMName:           m.modelName,
		InputContent:      request.Prompt,
		ContinuationCount: 0,
		IsComplete:        false,
		SafetyWarnings:    make([]string, 0),
	}

	// 執行智能續寫生成
	err := m.executeGenerationWithContinuation(ctx, request, response)
	if err != nil {
		return nil, err
	}

	// 計算生成時間
	response.GenerationTime = gtime.TimestampMilli() - startTime

	// 記錄完成日誌
	m.logger().Infof(ctx, "GenerateContent completed: model=%s, continuations=%d/%d, input_tokens=%d, output_tokens=%d, total_tokens=%d, complete=%v, duration=%dms",
		response.LLMName, response.ContinuationCount, request.MaxContinuations,
		response.InputTokens, response.OutputTokens, response.TotalTokens,
		response.IsComplete, response.GenerationTime)

	return response, nil
}

// applyDefaults 應用預設配置值到請求參數
// 確保所有必要的配置參數都有合理的預設值
// 參數：
//   - ctx: 上下文對象
//   - request: 內容生成請求對象
func (m *GeminiLLM) applyDefaults(ctx context.Context, request *llm.GenerateContentRequest) {
	// 設置最大續寫次數預設值
	if request.MaxContinuations <= 0 {
		defaultValue, _ := g.Cfg().Get(ctx, configKeyMaxContinuations, defaultMaxContinuations)
		request.MaxContinuations = gconv.Int(defaultValue)
		m.logger().Debugf(ctx, "Applied default MaxContinuations: %d", request.MaxContinuations)
	}

	// 設置總 token 預算預設值
	if request.TotalTokenBudget <= 0 {
		defaultValue, _ := g.Cfg().Get(ctx, configKeyTotalTokenBudget, m.maxOutputTokens*2)
		request.TotalTokenBudget = gconv.Int32(defaultValue)
		m.logger().Debugf(ctx, "Applied default TotalTokenBudget: %d", request.TotalTokenBudget)
	}

	// 設置溫度參數預設值
	if request.Temperature == nil {
		request.Temperature = &m.temperature
		m.logger().Debugf(ctx, "Applied default Temperature: %f", m.temperature)
	}
}

// executeGenerationWithContinuation 執行智能續寫生成
func (m *GeminiLLM) executeGenerationWithContinuation(ctx context.Context, request *llm.GenerateContentRequest, response *llm.GenerateContentResponse) error {
	var allContent strings.Builder
	var totalInputTokens, totalOutputTokens int32
	continuationCount := 0

	for continuationCount <= request.MaxContinuations {
		// 構建當前請求的 prompt
		currentPrompt := request.Prompt
		if continuationCount > 0 {
			currentPrompt = allContent.String() + "\n\n[請繼續完成上述內容]"
		}

		// 檢查 token 預算
		if totalInputTokens+totalOutputTokens >= request.TotalTokenBudget {
			m.logger().Debugf(ctx, "Token budget exceeded: %d/%d", totalInputTokens+totalOutputTokens, request.TotalTokenBudget)
			break
		}

		// 調用 Gemini API
		apiResponse, err := m.callGeminiAPI(ctx, currentPrompt, request)
		if err != nil {
			return err
		}

		// 處理響應
		processedResponse, err := m.processGeminiResponse(apiResponse, request.IncludeThinking)
		if err != nil {
			return err
		}

		// 累加內容和 token
		allContent.WriteString(processedResponse.OutputContent)
		totalInputTokens += processedResponse.InputTokens
		totalOutputTokens += processedResponse.OutputTokens

		// 合併安全警告
		response.SafetyWarnings = append(response.SafetyWarnings, processedResponse.SafetyWarnings...)

		// 保存思考過程（如果有）
		if request.IncludeThinking && !g.IsEmpty(processedResponse.ThinkingProcess) {
			if g.IsEmpty(response.ThinkingProcess) {
				response.ThinkingProcess = processedResponse.ThinkingProcess
			} else {
				response.ThinkingProcess += "\n\n" + processedResponse.ThinkingProcess
			}
		}

		// 保存完成原因
		response.FinishReason = processedResponse.FinishReason

		// 檢測是否需要續寫
		isComplete, reason := m.isContentComplete(allContent.String(), request.IncludeThinking)

		m.logger().Debugf(ctx, "Continuation %d: reason=%s, tokens_used=%d/%d, complete=%v",
			continuationCount, reason, totalInputTokens+totalOutputTokens, request.TotalTokenBudget, isComplete)

		if isComplete {
			response.IsComplete = true
			break
		}

		continuationCount++
	}

	// 設置最終響應數據
	response.OutputContent = allContent.String()
	response.InputTokens = totalInputTokens
	response.OutputTokens = totalOutputTokens
	response.TotalTokens = totalInputTokens + totalOutputTokens
	response.ContinuationCount = continuationCount

	return nil
}

// isContentComplete 檢測內容是否完整
func (m *GeminiLLM) isContentComplete(content string, includeThinking bool) (bool, string) {
	// 檢測未完成的程式碼區塊
	if gstr.Count(content, "```")%2 != 0 {
		return false, "incomplete_code_block"
	}

	// 檢測思考過程是否完整（如果啟用）
	if includeThinking && gstr.Contains(content, "<thinking>") && !gstr.Contains(content, "</thinking>") {
		return false, "incomplete_thinking"
	}

	// 檢測句子是否在合適位置結束
	trimmed := gstr.Trim(content)
	if g.IsEmpty(trimmed) {
		return false, "empty_content"
	}

	// 檢測常見的未完成標記
	for _, pattern := range incompleteContentPatterns {
		if gstr.ContainsI(content, pattern) {
			return false, "explicit_incomplete_marker"
		}
	}

	return true, "complete"
}

// callGeminiAPI 調用 Gemini API
func (m *GeminiLLM) callGeminiAPI(ctx context.Context, prompt string, request *llm.GenerateContentRequest) (*genai.GenerateContentResponse, error) {
	// 創建內容
	content := &genai.Content{
		Parts: []*genai.Part{
			{Text: prompt},
		},
		Role: genai.RoleUser,
	}

	// 創建生成配置
	config := &genai.GenerateContentConfig{
		Temperature:     request.Temperature,
		MaxOutputTokens: m.maxOutputTokens,
	}

	// 添加系統指令（如果有）
	if !g.IsEmpty(request.SystemInstruction) {
		config.SystemInstruction = genai.NewContentFromText(request.SystemInstruction, genai.RoleUser)
	}

	// 添加思考配置（如果啟用）
	if request.IncludeThinking {
		config.ThinkingConfig = &genai.ThinkingConfig{
			IncludeThoughts: true,
			ThinkingBudget:  genai.Ptr[int32](1000), // 預設思考預算
		}
	}

	var r *genai.GenerateContentResponse
	var err error

	// 重試邏輯，與 Chat 方法保持一致
	m.logger().Debugf(ctx, "🔄 Starting API call with %d retry attempts...", defaultRetryCount)
	for i := 0; i < defaultRetryCount; i++ {
		m.logger().Debugf(ctx, "🔄 API call attempt %d/%d to model: %s", i+1, defaultRetryCount, m.modelName)
		r, err = m.client.Models.GenerateContent(ctx, m.modelName, []*genai.Content{content}, config)

		if err != nil {
			m.logger().Errorf(ctx, "❌ API call attempt %d failed: %v", i+1, err)
			// 如果不是最後一次重試，等待後重試
			if i < defaultRetryCount-1 {
				vTtl, _ := g.Cfg().Get(ctx, "system.ai_send_retry_ttl", "40s")
				sleepDuration := vTtl.Duration()
				m.logger().Debugf(ctx, "⏳ Retrying in %v...", sleepDuration)
				time.Sleep(sleepDuration)
			}
		} else {
			m.logger().Debugf(ctx, "✅ API call successful on attempt %d", i+1)
			break
		}
	}

	if err != nil {
		return nil, gerror.WrapCode(consts.ApiFailed, err, "failed to call Gemini API after retries")
	}

	return r, nil
}

// processGeminiResponse 處理 Gemini API 的完整響應
func (m *GeminiLLM) processGeminiResponse(r *genai.GenerateContentResponse, includeThinking bool) (*llm.GenerateContentResponse, error) {
	if r == nil || len(r.Candidates) == 0 {
		return nil, gerror.New("empty response from Gemini API")
	}

	candidate := r.Candidates[0]
	response := &llm.GenerateContentResponse{
		LLMName:        m.modelName,
		SafetyWarnings: make([]string, 0),
	}

	// 處理完成原因
	if candidate.FinishReason != "" {
		response.FinishReason = string(candidate.FinishReason)
	}

	// 處理安全評級
	for _, rating := range candidate.SafetyRatings {
		if rating.Probability != genai.HarmProbabilityNegligible {
			response.SafetyWarnings = append(response.SafetyWarnings,
				gstr.Join([]string{string(rating.Category), string(rating.Probability)}, ": "))
		}
	}

	// 提取內容
	var contentBuilder strings.Builder
	var thinkingBuilder strings.Builder

	for _, part := range candidate.Content.Parts {
		if !g.IsEmpty(part.Text) {
			// 區分思考過程和正常內容
			if includeThinking && gstr.Contains(part.Text, "<thinking>") {
				thinkingBuilder.WriteString(part.Text)
			} else {
				contentBuilder.WriteString(part.Text)
			}
		}

		// 處理函數調用（如果有）
		if part.FunctionCall != nil {
			contentBuilder.WriteString(gstr.Join([]string{"[Function Call: ", part.FunctionCall.Name, "]"}, ""))
		}
	}

	response.OutputContent = contentBuilder.String()
	if includeThinking {
		response.ThinkingProcess = thinkingBuilder.String()
	}

	// 處理 token 統計
	if r.UsageMetadata != nil {
		response.InputTokens = r.UsageMetadata.PromptTokenCount
		response.OutputTokens = r.UsageMetadata.CandidatesTokenCount
		response.TotalTokens = r.UsageMetadata.TotalTokenCount
	}

	return response, nil
}

// GenerateContentSimple 簡化版本，向後兼容
func (m *GeminiLLM) GenerateContentSimple(ctx context.Context, prompt string) (string, error) {
	request := &llm.GenerateContentRequest{
		Prompt:           prompt,
		MaxContinuations: 3,
		TotalTokenBudget: m.maxOutputTokens * 2,
		IncludeThinking:  false,
	}

	response, err := m.GenerateContent(ctx, request)
	if err != nil {
		return "", err
	}

	return response.OutputContent, nil
}

// Release 釋放資源和清理連接
// 實現 ILLMs 接口的資源清理方法
// 參數：
//   - ctx: 上下文對象
func (m *GeminiLLM) Release(ctx context.Context) {
	m.logger().Debugf(ctx, "Releasing Gemini LLM resources")

	// 清理聊天會話
	if m.chat != nil {
		m.chat = nil
	}

	// 清理客戶端連接
	if m.client != nil {
		m.client = nil
	}

	// 清理配置和負載數據
	m.config = nil
	m.payload = nil

	m.logger().Infof(ctx, "Gemini LLM resources released successfully")
}

// ================================================================================================
// 輔助方法區域
// ================================================================================================

// setupGoogleCredentialsSimple 設置 Google 認證憑證（簡化版本）
// 只處理憑證文件路徑和環境變數設置，移除 NO_PROXY 操作
// 參數：
//   - ctx: 上下文對象
//   - params: LLM 配置參數
//
// 返回：
//   - error: 設置過程中的錯誤信息
func (m *GeminiLLM) setupGoogleCredentialsSimple(ctx context.Context, params *llm.LLMsConfig) error {
	m.logger().Debugf(ctx, "🔐 Starting Google credentials setup...")

	// 優先使用參數中指定的憑證文件
	if !g.IsEmpty(params.Vertex.CredentialFile) {
		credentialFile := params.Vertex.CredentialFile
		m.logger().Debugf(ctx, "🔐 Using credential file from config: %s", credentialFile)

		if gfile.IsFile(credentialFile) {
			// 設置環境變數
			err := os.Setenv(envGoogleApplicationCredentials, credentialFile)
			if err != nil {
				m.logger().Errorf(ctx, "❌ Failed to set GOOGLE_APPLICATION_CREDENTIALS: %v", err)
				return gerror.Wrap(err, "failed to set GOOGLE_APPLICATION_CREDENTIALS environment variable")
			}
			m.logger().Infof(ctx, "✅ Set GOOGLE_APPLICATION_CREDENTIALS from config: %s", credentialFile)
		} else {
			err := gerror.Newf("credential file specified in config does not exist: %s", credentialFile)
			m.logger().Errorf(ctx, "❌ Credential file not found: %s", credentialFile)
			return err
		}
	} else {
		// 檢查環境變數
		existingCredentials := os.Getenv(envGoogleApplicationCredentials)
		if !g.IsEmpty(existingCredentials) {
			m.logger().Infof(ctx, "🔐 Using existing GOOGLE_APPLICATION_CREDENTIALS: %s", existingCredentials)
			// 驗證文件是否存在
			if !gfile.IsFile(existingCredentials) {
				err := gerror.Newf("credential file from environment variable does not exist: %s", existingCredentials)
				m.logger().Errorf(ctx, "❌ Credential file from env var not found: %s", existingCredentials)
				return err
			}
		} else {
			err := gerror.New("no credential file specified in config and GOOGLE_APPLICATION_CREDENTIALS environment variable is not set")
			m.logger().Errorf(ctx, "❌ No credentials configured")
			return err
		}
	}

	m.logger().Debugf(ctx, "✅ Google credentials setup completed")
	return nil
}

// setupProxyConfiguration 設置代理配置（簡化版本）
// 直接從配置文件讀取代理設置，統一使用代理
// 參數：
//   - ctx: 上下文對象
//   - clientConfig: 客戶端配置對象
//
// 返回：
//   - error: 配置過程中的錯誤信息
func (m *GeminiLLM) setupProxyConfiguration(ctx context.Context, clientConfig *genai.ClientConfig) error {
	m.logger().Debugf(ctx, "🌐 Starting proxy configuration setup...")

	// 檢查是否需要設置代理
	vProxy, _ := g.Cfg().Get(ctx, configKeyProxyURL)
	if vProxy == nil || vProxy.IsEmpty() {
		m.logger().Infof(ctx, "🌐 No proxy configuration found, using direct connection")
		return nil
	}

	proxyURLString := vProxy.String()
	m.logger().Infof(ctx, "🌐 Found proxy configuration: %s", proxyURLString)

	proxyURL, parseErr := url.Parse(proxyURLString)
	if parseErr != nil {
		m.logger().Errorf(ctx, "❌ Failed to parse proxy URL '%s': %v", proxyURLString, parseErr)
		return gerror.Wrap(parseErr, "invalid proxy URL format")
	}

	m.logger().Infof(ctx, "🌐 Setting up proxy for Gemini client: %s", proxyURLString)

	// 創建簡單的代理 Transport，所有請求都使用代理
	transport := &http.Transport{
		Proxy: func(req *http.Request) (*url.URL, error) {
			m.logger().Debugf(ctx, "🌐 Using proxy for request: %s %s", req.Method, req.URL.String())
			return proxyURL, nil
		},
	}

	// 創建 HTTP 客戶端
	httpClient := &http.Client{
		Transport: transport,
		Timeout:   time.Duration(defaultClientTimeout) * time.Second,
	}

	// 設置 HTTP 客戶端到配置中
	clientConfig.HTTPClient = httpClient
	m.logger().Infof(ctx, "✅ Proxy configuration completed successfully")

	return nil
}
